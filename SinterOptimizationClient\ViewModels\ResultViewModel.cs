using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Win32;
using SinterOptimizationClient.Models;
using SinterOptimizationClient.Services;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SinterOptimizationClient.ViewModels
{
    public partial class ResultViewModel : ObservableObject
    {
        private readonly IDialogService _dialogService;

        [ObservableProperty]
        private OptimizationResult? currentResult;

        [ObservableProperty]
        private ObservableCollection<ResultSolution> comprehensiveSolutions = new();

        [ObservableProperty]
        private ObservableCollection<ResultSolution> currentSolutions = new();

        [ObservableProperty]
        private ResultSolution? selectedSolution;

        [ObservableProperty]
        private string algorithmInfo = "质量成本综合优化算法";

        [ObservableProperty]
        private string algorithmAccuracy = "计算精度±0.01%，智能平衡质量与成本";

        [ObservableProperty]
        private string calculationLogic = "基于SQP二次序列算法，同时优化TFe、碱度、MgO、Al₂O₃四项质量指标与成本控制，权重智能分配：质量70%，成本30%";

        [ObservableProperty]
        private bool isFormulaExpanded = true;

        [ObservableProperty]
        private string statusMessage = "暂无优化结果";

        public ResultViewModel(IDialogService dialogService)
        {
            _dialogService = dialogService;

            // 初始化状态消息
            StatusMessage = "暂无优化结果，请先进行优化计算";

            // 初始化空的解决方案集合
            ComprehensiveSolutions = new ObservableCollection<ResultSolution>();
            CurrentSolutions = new ObservableCollection<ResultSolution>();
            
            // 添加示例数据用于展示
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            try
            {
                // 创建综合优化示例数据
                var comprehensiveSample = new ResultSolution
                {
                    SolutionId = 1,
                    OptimizationType = "质量成本综合优化",
                    WetRatios = new Dictionary<string, double>
                    {
                        ["高炉返矿"] = 26.6,
                        ["印粉海娜"] = 23.8,
                        ["俄罗斯精粉"] = 15.3,
                        ["澳粉纵横"] = 11.0,
                        ["回收料"] = 10.0,
                        ["生石灰"] = 6.2,
                        ["焦粉"] = 3.0,
                        ["轻烧白云石"] = 2.1,
                        ["钢渣"] = 2.0
                    },
                    Cost = 650.8,
                    TFe = 55.00,
                    R = 1.903,
                    MgO = 2.41,
                    Al2O3 = 2.34,
                    TFeDeviation = 0.00,
                    RDeviation = 0.003,
                    ObjectiveValue = 0.0199,
                    Iterations = 342
                };

                ComprehensiveSolutions.Add(comprehensiveSample);

                // 显示综合优化结果
                CurrentSolutions.Add(comprehensiveSample);
                SelectedSolution = comprehensiveSample;
                StatusMessage = "当前显示: 质量成本综合优化结果 (示例数据)";
            }
            catch (Exception ex)
            {
                StatusMessage = $"加载示例数据时发生错误: {ex.Message}";
            }
        }

        public void SetOptimizationResult(OptimizationResult result)
        {
            try
            {
                CurrentResult = result;
                LoadSolutions();
                StatusMessage = result?.Success == true ? "优化计算完成" : "优化计算失败";
            }
            catch (Exception ex)
            {
                StatusMessage = $"加载优化结果时发生错误: {ex.Message}";
                _dialogService?.ShowError("错误", $"加载优化结果时发生错误: {ex.Message}");
            }
        }

        private void LoadSolutions()
        {
            try
            {
                ComprehensiveSolutions.Clear();
                CurrentSolutions.Clear();

                if (CurrentResult?.Success == true)
                {
                    var solutions = CurrentResult.GetSolutions();

                    if (solutions != null)
                    {
                        foreach (var solution in solutions)
                        {
                            if (solution != null)
                            {
                                // 新算法只有综合优化方案
                                solution.OptimizationType = "质量成本综合优化";
                                ComprehensiveSolutions.Add(solution);
                            }
                        }

                        // 显示综合优化结果
                        foreach (var solution in ComprehensiveSolutions)
                        {
                            CurrentSolutions.Add(solution);
                        }

                        SelectedSolution = CurrentSolutions.FirstOrDefault();
                        StatusMessage = $"优化计算完成，共生成 {CurrentSolutions.Count} 个综合优化方案";
                    }
                    else
                    {
                        StatusMessage = "优化结果数据为空";
                    }
                }
                else
                {
                    StatusMessage = CurrentResult?.ErrorMessage ?? "暂无优化结果";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"加载解决方案时发生错误: {ex.Message}";
                _dialogService?.ShowError("错误", $"加载解决方案时发生错误: {ex.Message}");
            }
        }

        [RelayCommand]
        private void RefreshVisualization()
        {
            try
            {
                // 刷新可视化展示
                if (SelectedSolution != null)
                {
                    StatusMessage = $"可视化已刷新 - {SelectedSolution.OptimizationType}";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"刷新可视化时发生错误: {ex.Message}";
                _dialogService?.ShowError("错误", $"刷新可视化时发生错误: {ex.Message}");
            }
        }

        [RelayCommand]
        private void ShowOptimizationDetails()
        {
            try
            {
                if (SelectedSolution != null)
                {
                    var details = BuildOptimizationDetails(SelectedSolution);
                    _dialogService.ShowInformation("综合优化详情", details);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"显示优化详情时发生错误: {ex.Message}";
                _dialogService?.ShowError("错误", $"显示优化详情时发生错误: {ex.Message}");
            }
        }

        [RelayCommand]
        private void ViewSolutionDetails(ResultSolution? solution)
        {
            if (solution == null) return;

            var details = BuildSolutionDetails(solution);
            _dialogService.ShowInformation($"方案 {solution.SolutionId} 详细信息", details);
        }

        [RelayCommand]
        private void ToggleFormulaExpansion()
        {
            IsFormulaExpanded = !IsFormulaExpanded;
        }

        [RelayCommand]
        private async Task ExportResults()
        {
            try
            {
                if (CurrentResult == null || !CurrentResult.Success)
                {
                    _dialogService.ShowWarning("导出警告", "暂无可导出的优化结果");
                    return;
                }

                var saveFileDialog = new SaveFileDialog
                {
                    Title = "导出优化结果",
                    Filter = "Excel文件 (*.xlsx)|*.xlsx|CSV文件 (*.csv)|*.csv",
                    DefaultExt = "xlsx",
                    FileName = $"优化结果_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    StatusMessage = "正在导出结果...";

                    var extension = Path.GetExtension(saveFileDialog.FileName).ToLower();
                    if (extension == ".csv")
                    {
                        await ExportToCsv(saveFileDialog.FileName);
                    }
                    else
                    {
                        await ExportToExcel(saveFileDialog.FileName);
                    }

                    StatusMessage = "结果导出完成";
                    _dialogService.ShowInformation("导出成功", $"优化结果已导出到: {saveFileDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "导出失败";
                _dialogService.ShowError("导出错误", $"导出结果时发生错误: {ex.Message}");
            }
        }

        [RelayCommand]
        private void RefreshResults()
        {
            if (CurrentResult != null)
            {
                LoadSolutions();
                StatusMessage = "结果已刷新";
            }
        }

        private string BuildSolutionDetails(ResultSolution solution)
        {
            var details = new StringBuilder();
            details.AppendLine($"方案编号: {solution.SolutionId}");
            details.AppendLine($"优化类型: {solution.OptimizationType}");
            details.AppendLine($"目标函数值: {solution.ObjectiveValue:F6}");
            details.AppendLine($"迭代次数: {solution.Iterations}");
            details.AppendLine();

            details.AppendLine("湿配比详情:");
            foreach (var ratio in solution.WetRatios.OrderByDescending(kvp => kvp.Value))
            {
                if (ratio.Value > 0.01)
                {
                    details.AppendLine($"  {ratio.Key}: {ratio.Value:F2}%");
                }
            }
            details.AppendLine();

            details.AppendLine("烧结矿成分:");
            details.AppendLine($"  TFe: {solution.TFe:F2}%");
            details.AppendLine($"  碱度R: {solution.R:F3}");
            details.AppendLine($"  MgO: {solution.MgO:F2}%");
            details.AppendLine($"  Al₂O₃: {solution.Al2O3:F2}%");
            details.AppendLine();

            details.AppendLine($"单位成本: {solution.Cost:F2} 元/吨");

            details.AppendLine();
            details.AppendLine("综合优化偏差:");
            details.AppendLine($"  TFe偏差: {solution.FormattedTFeDeviation}%");
            details.AppendLine($"  R偏差: {solution.FormattedRDeviation}");

            return details.ToString();
        }

        private string BuildOptimizationDetails(ResultSolution solution)
        {
            var details = new StringBuilder();
            details.AppendLine("🎯 质量成本综合优化详情");
            details.AppendLine("=" * 50);
            details.AppendLine();

            details.AppendLine("📊 算法信息:");
            details.AppendLine($"  算法类型: SQP二次序列算法");
            details.AppendLine($"  优化目标: 质量成本综合优化");
            details.AppendLine($"  计算精度: ±0.01%");
            details.AppendLine($"  迭代次数: {solution.Iterations}");
            details.AppendLine($"  目标函数值: {solution.ObjectiveValue:F6}");
            details.AppendLine();

            details.AppendLine("🎯 优化权重配置:");
            details.AppendLine($"  质量指标权重: 70%");
            details.AppendLine($"    - TFe权重: 50%");
            details.AppendLine($"    - 碱度R权重: 30%");
            details.AppendLine($"    - MgO权重: 10%");
            details.AppendLine($"    - Al₂O₃权重: 10%");
            details.AppendLine($"  成本控制权重: 30%");
            details.AppendLine();

            details.AppendLine("📈 优化结果:");
            details.AppendLine($"  总成本: {solution.Cost:F2} 元/吨");
            details.AppendLine($"  TFe含量: {solution.TFe:F2}% (目标: 55.0%)");
            details.AppendLine($"  碱度R: {solution.R:F3} (目标: 1.90)");
            details.AppendLine($"  MgO含量: {solution.MgO:F2}% (目标: 2.39%)");
            details.AppendLine($"  Al₂O₃含量: {solution.Al2O3:F2}% (目标: 1.89%)");
            details.AppendLine();

            details.AppendLine("📦 主要物料配比:");
            foreach (var ratio in solution.WetRatios.OrderByDescending(kvp => kvp.Value))
            {
                if (ratio.Value > 0.1)
                {
                    details.AppendLine($"  {ratio.Key}: {ratio.Value:F2}%");
                }
            }
            details.AppendLine();

            details.AppendLine("✅ 质量达标分析:");
            var tfeAchievement = Math.Max(0, 100 - Math.Abs(solution.TFe - 55.0) / 55.0 * 100);
            var rAchievement = Math.Max(0, 100 - Math.Abs(solution.R - 1.90) / 1.90 * 100);
            var mgoAchievement = Math.Max(0, 100 - Math.Abs(solution.MgO - 2.39) / 2.39 * 100);
            var al2o3Achievement = Math.Max(0, 100 - Math.Abs(solution.Al2O3 - 1.89) / 1.89 * 100);
            
            details.AppendLine($"  TFe达成度: {tfeAchievement:F1}%");
            details.AppendLine($"  碱度R达成度: {rAchievement:F1}%");
            details.AppendLine($"  MgO达成度: {mgoAchievement:F1}%");
            details.AppendLine($"  Al₂O₃达成度: {al2o3Achievement:F1}%");
            
            var overallAchievement = (tfeAchievement + rAchievement + mgoAchievement + al2o3Achievement) / 4;
            details.AppendLine($"  综合达成度: {overallAchievement:F1}%");

            return details.ToString();
        }

        private async Task ExportToCsv(string fileName)
        {
            var csv = new StringBuilder();

            // 添加表头
            csv.AppendLine("方案,优化类型,湿配比,成本(元/吨),TFe(%),碱度R,MgO(%),Al2O3(%),TFe偏差(%),R偏差,目标函数值,迭代次数");

            // 添加综合优化结果
            foreach (var solution in ComprehensiveSolutions)
            {
                csv.AppendLine($"{solution.SolutionId},{solution.OptimizationType},\"{solution.FormattedWetRatios}\",{solution.Cost:F2},{solution.TFe:F2},{solution.R:F3},{solution.MgO:F2},{solution.Al2O3:F2},{solution.FormattedTFeDeviation},{solution.FormattedRDeviation},{solution.ObjectiveValue:F6},{solution.Iterations}");
            }

            await File.WriteAllTextAsync(fileName, csv.ToString(), Encoding.UTF8);
        }

        private async Task ExportToExcel(string fileName)
        {
            // 这里应该实现Excel导出功能
            // 暂时使用CSV格式
            await ExportToCsv(fileName.Replace(".xlsx", ".csv"));
        }

        // 计算公式集合
        public List<string> CalculationFormulas => new()
        {
            "干料量 = 配料比（%）×（1 - 物理水（%）/100）",
            "总干料量 = Σ 各原料干料量",
            "烧成量 = 干料量 ×（1 - 烧损率（%）/100）",
            "TFe（%）= Σ（原料干料量 × 原料TFe（%））/Σ 烧成量 ×100",
            "Ro = ΣCaO/ΣSiO₂ 或 (ΣCaO+ΣMgO)/(ΣSiO₂+ΣAl₂O₃)"
        };
    }
}
